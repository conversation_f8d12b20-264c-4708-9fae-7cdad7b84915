<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Gestione Clienti</h1>
            <p class="text-muted">Gestisci i clienti dell'agenzia</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo e(route('clients.create')); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nuovo Cliente
            </a>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('clients.index')); ?>">
                <div class="row">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" placeholder="Cerca per nome, email, telefono, P.IVA..." value="<?php echo e(request('search')); ?>">
                    </div>
                    <div class="col-md-3">
                        <select name="tipo" class="form-select">
                            <option value="">Tutti i tipi</option>
                            <option value="Persona Fisica" <?php echo e(request('tipo') == 'Persona Fisica' ? 'selected' : ''); ?>>Persona Fisica</option>
                            <option value="Persona Giuridica" <?php echo e(request('tipo') == 'Persona Giuridica' ? 'selected' : ''); ?>>Persona Giuridica</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i> Cerca
                        </button>
                        <a href="<?php echo e(route('clients.index')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Clients Table -->
    <div class="card">
        <div class="card-body">
            <?php if($clients->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Nome/Ragione Sociale</th>
                                <th>Tipo</th>
                                <th>Contatti</th>
                                <th>Progetti</th>
                                <th>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <strong><?php echo e($client->full_name); ?></strong>
                                    <?php if($client->partita_iva): ?>
                                        <br><small class="text-muted">P.IVA: <?php echo e($client->partita_iva); ?></small>
                                    <?php endif; ?>
                                    <?php if($client->codice_fiscale): ?>
                                        <br><small class="text-muted">C.F.: <?php echo e($client->codice_fiscale); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge <?php echo e($client->tipo == 'Persona Fisica' ? 'bg-info' : 'bg-success'); ?>">
                                        <?php echo e($client->tipo); ?>

                                    </span>
                                </td>
                                <td>
                                    <?php if($client->email): ?>
                                        <div><i class="fas fa-envelope"></i> <?php echo e($client->email); ?></div>
                                    <?php endif; ?>
                                    <?php if($client->telefono): ?>
                                        <div><i class="fas fa-phone"></i> <?php echo e($client->telefono); ?></div>
                                    <?php endif; ?>
                                    <?php if($client->indirizzo): ?>
                                        <div><i class="fas fa-map-marker-alt"></i> <?php echo e(Str::limit($client->indirizzo, 30)); ?></div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?php echo e($client->projects_count); ?> progetti</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('clients.show', $client)); ?>" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('clients.edit', $client)); ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="<?php echo e(route('clients.destroy', $client)); ?>" method="POST" class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                    onclick="return confirm('Sei sicuro di voler eliminare questo cliente?')"
                                                    <?php echo e($client->projects_count > 0 ? 'disabled title=Cliente con progetti associati' : ''); ?>>
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    <?php echo e($clients->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-handshake fa-3x text-muted mb-3"></i>
                    <h5>Nessun cliente trovato</h5>
                    <p class="text-muted">Non ci sono clienti che corrispondono ai criteri di ricerca.</p>
                    <a href="<?php echo e(route('clients.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Aggiungi il primo cliente
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Lavori\36k\gestionale\laravel\resources\views/clients/index.blade.php ENDPATH**/ ?>